<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抽卡模拟器</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;700&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@500;700;900&family=Audiowide&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6d28d9;
            --primary-hover: #5b21b6;
            --secondary-color: #1e293b;
            --accent-color: #60a5fa;
        }
        
        body {
            font-family: 'Noto Sans SC', 'Montserrat', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            background-attachment: fixed;
            color: #e2e8f0;
            position: relative;
        }
        
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.04) 1px, transparent 1px),
                radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.04) 1px, transparent 1px);
            background-size: 50px 50px;
            pointer-events: none;
            z-index: 0;
        }
        
        .custom-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(to right, rgba(30, 41, 59, 0.9), rgba(109, 40, 217, 0.8));
            border: 1px solid rgba(96, 165, 250, 0.3);
            color: #e2e8f0;
            padding: 15px 20px;
            border-radius: 8px;
            font-size: 16px;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 20px rgba(0,0,0,0.4), 0 0 30px rgba(96, 165, 250, 0.3);
            transition: opacity 0.4s ease-out;
            opacity: 0;
            white-space: nowrap;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }
        .custom-message.is-visible {
            opacity: 1;
        }
        
        /* 高级标题样式 */
        header {
            background: linear-gradient(to right, rgba(0,0,0,0.9), rgba(0,0,0,0.7));
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 20px rgba(0,0,0,0.5);
            position: relative;
            overflow: hidden;
            z-index: 10;
        }
        
        header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
            opacity: 0.8;
            z-index: 5;
        }
        
        /* 总值显示改进 */
        #totalValue {
            background: rgba(15, 23, 42, 0.9);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            z-index: 50;
        }
        
        #totalValueCounter {
            font-family: 'Orbitron', sans-serif;
            font-weight: 700;
            text-shadow: 0 0 10px rgba(239, 68, 68, 0.8);
            letter-spacing: 1px;
            background: linear-gradient(90deg, #ff4d4d, #f87171);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { text-shadow: 0 0 10px rgba(239, 68, 68, 0.5); }
            50% { text-shadow: 0 0 20px rgba(239, 68, 68, 0.8); }
            100% { text-shadow: 0 0 10px rgba(239, 68, 68, 0.5); }
        }
        
        /* 按钮样式增强 */
        #drawButton {
            background: linear-gradient(135deg, #8b5cf6 0%, #6d28d9 100%);
            border: none;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease, transform 0.2s ease;
            box-shadow: 0 8px 16px rgba(109, 40, 217, 0.3);
        }
        
        #drawButton::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.7s ease;
        }
        
        #drawButton:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(109, 40, 217, 0.4);
        }
        
        #drawButton:hover::before {
            left: 100%;
        }
        
        #drawButton:disabled {
            background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
            transform: none;
            box-shadow: 0 4px 8px rgba(107, 114, 128, 0.3);
        }
        
        #testButton {
            background: rgba(55, 65, 81, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        #testButton:hover {
            background: rgba(75, 85, 99, 0.9);
            box-shadow: 0 6px 16px rgba(0,0,0,0.2);
        }
        
        /* 应用区域优化 */
        #app {
            padding: 1.5rem;
            min-height: 200px;
            position: relative;
            z-index: 1;
        }
        
        /* 测试结果区域美化 */
        #testResultSection {
            background: linear-gradient(to bottom right, rgba(30, 41, 59, 0.9), rgba(15, 23, 42, 0.9));
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
            box-shadow: 0 10px 25px rgba(0,0,0,0.3);
            backdrop-filter: blur(5px);
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        #testResultOutput {
            background: rgba(17, 24, 39, 0.7);
            border-color: rgba(55, 65, 81, 0.5);
            transition: all 0.3s ease;
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
        }
        
        /* 转盘区域优化 */
        .turntable-section {
            position: relative;
            padding: 2rem 0;
            margin-top: 3rem;
            border-top: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        #turntable-iframe {
            transition: all 0.3s ease;
            box-shadow: 0 15px 30px rgba(0,0,0,0.4);
        }
        
        #spinResult {
            font-size: 1.125rem;
            font-weight: 500;
            transition: all 0.3s ease;
            padding: 0.5rem;
            text-align: center;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {}
            }
        }
    </script>
</head>

<body class="flex flex-col items-center min-h-screen font-sans text-white">
    <script>
        const apiBaseUrl = '/api';
    </script>
    <header class="w-full py-4 shadow-lg">
        <div class="container mx-auto flex items-center justify-between px-4">
            <div class="flex items-center">
                <img src="logo.jpg" alt="Mon电竞 Logo" class="h-10 sm:h-12 mr-3 sm:mr-4 rounded-lg shadow-md">
                <div>
                    <h1 class="text-xl sm:text-2xl font-bold text-white">Mon电竞</h1>
                    <p class="text-xs sm:text-sm text-blue-300">礼拜一电竞</p>
                </div>
            </div>
        </div>
    </header>

    <div id="totalValue" class="sticky top-0 w-full py-3 shadow-md z-50 text-center text-2xl text-gray-200 font-bold"></div>
    <div class="py-8 text-center">
        <button id="drawButton" class="px-8 py-4 text-lg font-bold text-white rounded-full cursor-pointer transition-all duration-200 shadow-lg hover:-translate-y-px hover:shadow-xl disabled:bg-gray-500 disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none">抽取25张卡</button>
        <!-- <button id="testButton" class="ml-4 px-8 py-4 text-lg font-bold rounded-full cursor-pointer text-white">运行测试</button> -->
    </div>
    <div id="app" class="flex flex-wrap gap-3 p-3 justify-center max-w-full"></div>

    <div id="testResultSection" class="hidden mx-auto my-5 p-6 rounded-xl w-11/12 sm:w-4/5 max-w-4xl shadow-md">
        <h2 class="text-xl font-bold mb-4 text-center text-gray-200">测试结果</h2>
        <pre id="testResultOutput" class="text-left p-5 rounded-md border whitespace-pre-wrap break-words font-mono text-sm text-gray-300"></pre>
    </div>

    <div class="turntable-section my-8 w-full px-6">
        <iframe id="turntable-iframe" src="turntable-component.html" class="w-full max-w-3xl h-[560px] border-0 rounded-xl mx-auto shadow-2xl"></iframe>
        <div id="spinResult" class="text-center mt-6 text-lg"></div>
    </div>
    <script>
        // Backend logic is now moved to server.js

        // 添加备用的数字动画函数
        class SimpleCountUp {
            constructor(targetId, startVal, options = {}) {
                this.targetId = targetId;
                this.startVal = startVal || 0;
                this.endVal = startVal || 0;
                this.duration = options.duration || 2;
                this.separator = options.separator || ',';
                this.element = document.getElementById(targetId);
                this.requestId = null;
                this.startTime = null;
            }

            formatNumber(num) {
                return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, this.separator);
            }

            update(newEndVal) {
                this.endVal = newEndVal;
                if (this.startTime === null) {
                    this.startVal = 0;
                    this.start();
                } else {
                    this.startVal = parseFloat(this.element.textContent.replace(/,/g, ''));
                    cancelAnimationFrame(this.requestId);
                    this.start();
                }
            }

            start() {
                if (!this.element) return;
                
                this.startTime = performance.now();
                
                const animateCount = (timestamp) => {
                    if (!this.startTime) this.startTime = timestamp;
                    
                    const progress = Math.min((timestamp - this.startTime) / (this.duration * 1000), 1);
                    const currentVal = this.startVal + progress * (this.endVal - this.startVal);
                    
                    if (this.element) {
                        this.element.textContent = this.formatNumber(Math.round(currentVal));
                    }
                    
                    if (progress < 1) {
                        this.requestId = requestAnimationFrame(animateCount);
                    }
                };
                
                this.requestId = requestAnimationFrame(animateCount);
            }
        }

        let isDrawing = false;
        let totalValue = 0;
        let turntableReady = false;
        let userId = null;
        
        // CountUp实例
        let totalCountUp = null;

        function createProductCard(product) {
            return `
                <div class="product-card ${product.theme} w-36">
                    <div class="card-value-popup">+${product.avgPrice ? product.avgPrice.toLocaleString() : '0'}</div>
                    <div class="product-card-inner">
                        <div class="product-card-front">
                            <div class="product-card-background"></div>
                            <div class="product-card-back-content">
                                <div class="back-header">
                                    <div class="back-title-en">DELTA SQUAD</div>
                                    <div class="back-title-cn">摸金卡</div>
                                </div>
                                <div class="back-logo">
                                    <svg width="100" height="80" viewBox="0 0 120 100" xmlns="http://www.w3.org/2000/svg" fill="white">
                                        <g transform="translate(10, 15)"><path d="M50 0 L100 35 L50 70 L0 35 Z M50 5 L5 35 L50 65 L95 35 Z" /><path d="M50 12 L25 35 L50 58 Z" /></g>
                                        <g transform="translate(30, 0)"><path d="M50 0 L100 35 L50 70 L0 35 Z M50 5 L5 35 L50 65 L95 35 Z" /><path d="M50 12 L25 35 L50 58 Z" /></g>
                                    </svg>
                                </div>
                                <div class="back-footer">SHAN JIAO ZHO</div>
                            </div>
                        </div>
                        <div class="product-card-back">
                            <div class="product-card-background"></div>
                            <div class="product-card-content flex flex-col justify-between">
                                <div class="product-header">
                                    <div class="product-name-label">名称/NAME</div>
                                    <div class="product-name-cn">${product.objectName}</div>
                                    <div class="product-name-en">DELTA SQUAD</div>
                                </div>
                                <div class="product-image-container flex-grow flex items-center justify-center">
                                    <img src="${product.prePic}" alt="${product.objectName}" class="product-image">
                                    <div class="brand-logo">DELTA SQUAD</div>
                                </div>
                                <div class="product-footer">
                                    <div class="product-avgPrice-container">
                                        <div class="product-avgPrice-label">哈弗币价值/avgPrice</div>
                                        <div class="product-avgPrice">${product.avgPrice ? product.avgPrice.toLocaleString() : '--'}</div>
                                        <div class="product-weight">${product.weight ? `${product.weight} kg` : '-'}</div>
                                    </div>
                                    <div class="grid-icon">
                                        ${(() => {
                                            let cells = '';
                                            for (let row = 0; row < 4; row++) {
                                                for (let col = 0; col < 4; col++) {
                                                    const isHighlighted = row < product.width && col < product.length;
                                                    cells += `<div class="grid-cell ${isHighlighted ? 'highlight' : ''}"></div>`;
                                                }
                                            }
                                            return cells;
                                        })()}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        function showMessage(message, duration = 3000) {
            document.querySelectorAll('.custom-message').forEach(el => el.remove());

            const messageEl = document.createElement('div');
            messageEl.className = 'custom-message';
            messageEl.textContent = message;
            
            document.body.appendChild(messageEl);

            requestAnimationFrame(() => {
                requestAnimationFrame(() => {
                    messageEl.classList.add('is-visible');
                });
            });

            setTimeout(() => {
                messageEl.classList.remove('is-visible');
                messageEl.addEventListener('transitionend', () => {
                    messageEl.remove();
                });
            }, duration);
        }

        async function displayAndFlipCards(cards) {
            const app = document.getElementById('app');
            const cardElements = [];
            let runningTotal = 0;
            const drawButton = document.getElementById('drawButton');
            const totalCards = cards.length;

            for (const [i, card] of cards.entries()) {
                drawButton.textContent = `正在抽取 (${totalCards - i})`;
                const cardHTML = createProductCard(card);
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = cardHTML.trim();
                const cardElement = tempDiv.firstChild;
                app.appendChild(cardElement);
                cardElements.push(cardElement);
                await sleep(150);
            }

            await sleep(500);

            drawButton.textContent = '正在翻开卡牌...';

            // 翻牌并显示价值动画
            for (const [index, cardElement] of cardElements.entries()) {
                const card = cards[index];
                const cardValue = Number(card.avgPrice) || 0;

                // 新增：检查卡片是否在可视区域内，如果不在则平滑滚动
                const rect = cardElement.getBoundingClientRect();
                const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
                const headerHeight = document.getElementById('totalValue').offsetHeight || 0; // 考虑顶部粘性导航栏的高度

                // 如果卡片顶部在视口之上（或被导航栏遮挡），或卡片底部在视口之下
                if (rect.top < headerHeight || rect.bottom > viewportHeight) {
                    cardElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center' // 'center' 会将元素滚动到视口中央
                    });
                    // 等待滚动动画大致完成
                    await sleep(500);
                }

                cardElement.classList.add('is-flipped');
                
                // 等待一点点时间让卡片翻转效果开始
                await sleep(100);
                
                // 显示价值弹出
                if (cardValue > 0) {
                    runningTotal += cardValue;
                    const popup = cardElement.querySelector('.card-value-popup');
                    popup.classList.add('show');
                    
                    // 更新总价值动画
                    updateTotalValue(runningTotal);
                    
                    // 3秒后隐藏弹窗
                    setTimeout(() => {
                        popup.classList.remove('show');
                    }, 3000);
                }
                
                // 等待翻转完成
                await sleep(200);
            }

            totalValue = runningTotal; // 更新总价值

            const typeCounts = {
                red: 0,
                yellow: 0,
                purple: 0,
                turntable: 0,
                blue: 0,
                unknown: 0
            };

            cards.forEach(card => {
                if (card.theme === 'theme-red') typeCounts.red++;
                else if (card.theme === 'theme-yellow') typeCounts.yellow++;
                else if (card.theme === 'theme-purple') typeCounts.purple++;
                else if (card.theme === 'theme-colorful') typeCounts.turntable++;
                else if (card.theme === 'theme-blue') typeCounts.blue++;
                else typeCounts.unknown++;
            });

            console.log('--- 本次抽取结果统计 ---');
            const totalDrawn = cards.length;
            if (totalDrawn > 0) {
                const typeNameMap = {
                    red: '红卡',
                    yellow: '金卡',
                    purple: '紫卡',
                    turntable: '转盘卡',
                    blue: '蓝卡'
                };
                for (const type in typeCounts) {
                    if (typeCounts[type] > 0) {
                        const probability = (typeCounts[type] / totalDrawn * 100).toFixed(2);
                        const typeName = typeNameMap[type] || '未知';
                        console.log(`${typeName}: ${typeCounts[type]} 张, 占比: ${probability}%`);
                    }
                }
            }
            console.log('------------------------');
        }

        function initTotalValueCounter() {
            // 创建固定布局，避免文字跳动
            const totalValueEl = document.getElementById('totalValue');
            
            // 清空现有内容
            totalValueEl.innerHTML = '';
            
            // 创建三个固定宽度的列，确保布局稳定
            const container = document.createElement('div');
            container.className = 'grid grid-cols-3 w-full';
            container.style.gridTemplateColumns = '1fr 200px 1fr';
            
            // 添加"总价值"文本元素
            const labelBefore = document.createElement('div');
            labelBefore.className = 'text-right';
            const labelBeforeText = document.createElement('span');
            labelBeforeText.style.fontFamily = 'Audiowide, cursive';
            labelBeforeText.textContent = '总价值:';
            labelBefore.appendChild(labelBeforeText);
            
            // 添加数字显示容器（中间列）
            const counterContainer = document.createElement('div');
            counterContainer.className = 'flex justify-center items-center';
            
            // 添加数字显示元素
            const counter = document.createElement('span');
            counter.id = 'totalValueCounter';
            counter.className = 'text-red-500';
            counter.textContent = '0';
            counter.style.fontFamily = 'Orbitron, monospace';
            // 确保宽度稳定但保持数字清晰可见
            counter.style.minWidth = '180px';
            counter.style.display = 'block';
            counter.style.textAlign = 'center';
            counter.style.letterSpacing = '1px';
            counter.style.fontWeight = 'bold';
            counter.style.fontSize = '1.25rem';
            counter.style.textShadow = '0 0 10px rgba(239, 68, 68, 0.8)';
            counterContainer.appendChild(counter);
            
            // 添加"哈弗币"文本元素
            const labelAfter = document.createElement('div');
            labelAfter.className = 'text-left';
            const labelAfterText = document.createElement('span');
            labelAfterText.style.fontFamily = 'Audiowide, cursive';
            labelAfterText.textContent = '哈弗币';
            labelAfter.appendChild(labelAfterText);
            
            // 将三个列添加到容器
            container.appendChild(labelBefore);
            container.appendChild(counterContainer);
            container.appendChild(labelAfter);
            
            // 将容器添加到总价值元素
            totalValueEl.appendChild(container);
            
            // 初始化SimpleCountUp
            totalCountUp = new SimpleCountUp('totalValueCounter', 0, {
                duration: 1,
                separator: ','
            });
            totalCountUp.start();
        }

        async function runTest() {
            if (isDrawing) {
                return;
            }

            const testButton = document.getElementById('testButton');
            testButton.disabled = true;

            const testResultSection = document.getElementById('testResultSection');
            const testResultOutput = document.getElementById('testResultOutput');

            testResultSection.style.display = 'block';
            testResultOutput.textContent = `正在请求后端进行测试，请稍候...`;

            try {
                const response = await fetch(`${apiBaseUrl}/Mon/test?id=${userId}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();

                let resultText = `--- ${data.numTests}次抽奖测试结果 ---\n`;
                resultText += `最高单次价值: ${data.maxValue.toLocaleString()}\n`;
                resultText += `最低单次价值: ${data.minValue.toLocaleString()}\n`;
                resultText += `平均每次抽奖价值: ${data.averageValue.toLocaleString()}\n`;
                resultText += '------------------------\n';
                data.drawValues.forEach((value, index) => {
                    resultText += `第 ${index + 1} 次抽奖价值: ${value.toLocaleString()}\n`;
                });

                testResultOutput.textContent = resultText;

            } catch(e) {
                console.error("Test failed:", e);
                testResultOutput.textContent = `测试失败: ${e.message}`;
            } finally {
                testButton.disabled = false;
            }
        }

        async function handleDraw() {
            if (isDrawing) return;
            isDrawing = true;

            const drawButton = document.getElementById('drawButton');
            const app = document.getElementById('app');
            const turntableIframeEl = document.getElementById('turntable-iframe');
            
            drawButton.disabled = true;
            drawButton.textContent = '正在请求...';
            app.innerHTML = '';
            
            // 完全重置状态
            initTotalValueCounter();
            totalValue = 0;
            turntableReady = false;
            
            // 重置并隐藏转盘区域
            const turntableSection = document.querySelector('.turntable-section');
            if (turntableSection) {
                turntableSection.style.display = 'none';
            }
            if (turntableIframeEl) {
                // 通过重置src来强制iframe重新加载，清除旧状态
                const currentSrc = turntableIframeEl.src;
                turntableIframeEl.src = 'about:blank';
                setTimeout(() => { turntableIframeEl.src = currentSrc; }, 0);
            }

            try {
                const response = await fetch(`${apiBaseUrl}/Mon/draw?id=${userId}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                
                const drawnCards = data.cards;

                if (data.alreadyDrawn) {
                    showMessage('您已经抽过卡了，为您显示上次的结果', 3000);
                }

                if(drawnCards.length > 0) {
                    await displayAndFlipCards(drawnCards);
                }

                const agentCardsDrawn = data.turntableSpins || 0;
                // 如果是历史记录或有转盘次数，则初始化转盘
                if (data.alreadyDrawn || agentCardsDrawn > 0) {
                    const turntableSection = document.querySelector('.turntable-section');
                    if (turntableSection) {
                        turntableSection.style.display = 'block';
                        await sleep(500);
                        turntableSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }

                    // 延迟一点时间确保iframe重新加载完毕，然后发送初始化消息
                    setTimeout(() => {
                        const turntableIframe = document.getElementById('turntable-iframe').contentWindow;
                        turntableIframe.postMessage({ type: 'initialize' }, '*');
                    }, 200);

                    // 只在首次抽卡时给予提示
                    if (!data.alreadyDrawn && agentCardsDrawn > 0) {
                        showMessage(`恭喜！获得 ${agentCardsDrawn} 次转盘机会！`);
                    }

                    const spinResultEl = document.getElementById('spinResult');
                    if (spinResultEl) {
                        spinResultEl.textContent = '';
                    }
                }
            } catch (e) {
                console.error("Draw failed:", e);
                showMessage(`抽奖失败: ${e.message}`, 5000);
            }

            drawButton.textContent = '重新播放动画';
            drawButton.disabled = false;
            isDrawing = false;
        }

        function updateTotalValue(newValue) {
            if (totalCountUp) {
                if (typeof totalCountUp.update === 'function') {
                    totalCountUp.update(newValue);
                }
            } else {
                const counterElement = document.getElementById('totalValueCounter');
                if (counterElement) {
                    counterElement.textContent = newValue.toLocaleString();
                }
            }
        }
        
        document.addEventListener('DOMContentLoaded', async () => {
            const urlParams = new URLSearchParams(window.location.search);
            userId = urlParams.get('id');

            if (!userId) {
                showMessage('未查询到此订单', 10000);
                document.getElementById('drawButton').disabled = true;
                document.getElementById('testButton').disabled = true;
                return;
            }

            const turntableIframe = document.getElementById('turntable-iframe');
            if (turntableIframe && userId) {
                turntableIframe.src = `turntable-component.html?id=${userId}`;
            }

            initTotalValueCounter();
            
            window.addEventListener('message', (event) => {
                const turntableIframe = document.getElementById('turntable-iframe').contentWindow;
                if (event.source !== turntableIframe) {
                    return;
                }

                if (event.data.type === 'turntableReady') {
                    turntableReady = true;
                }

                if (event.data.type === 'initialSpinsValue') {
                    const historicalSpinValue = event.data.value;
                    if (historicalSpinValue > 0) {
                        totalValue += historicalSpinValue;
                        updateTotalValue(totalValue);
                    }
                }

                if (event.data.type === 'spinResult') {
                    const prize = event.data.prize;
                    const spinResultEl = document.getElementById('spinResult');
                    if (prize && prize.value > 0) {
                        // 使用动画更新总值
                        const oldTotal = totalValue;
                        totalValue += prize.value;
                        
                        updateTotalValue(totalValue);
                        
                        if (spinResultEl) {
                            spinResultEl.innerHTML = `恭喜！转盘抽中 <span style="color: #ffc107; font-weight: bold;">${prize.name}</span>哈弗币！`;
                            spinResultEl.style.color = '#28a745';
                        }
                    } else if (spinResultEl) {
                        spinResultEl.textContent = '';
                    }
                }
            });

            document.getElementById('drawButton').addEventListener('click', handleDraw);
            document.getElementById('testButton').addEventListener('click', runTest);
        });
    </script>
</body>

</html>

