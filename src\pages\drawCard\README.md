# DrawCard Vue 组件

这是一个完全基于Vue 3的抽卡模拟器组件，替代了原来的HTML+JavaScript实现。

## 主要改进

### 1. 舍弃Tailwind CSS
- 移除了所有Tailwind CSS类
- 使用原生SCSS编写所有样式
- 保持了原有的视觉效果和动画

### 2. 替代DOM操作
- 将原来的`createProductCard`函数转换为Vue模板
- 使用Vue响应式数据替代直接DOM操作
- 使用Vue事件处理替代addEventListener

### 3. 父子组件通信
- 移除了iframe和postMessage通信
- 使用Vue的props和emit实现父子组件通信
- 转盘组件作为子组件集成到主组件中

## 组件结构

### 主组件 (index.vue)
- **功能**: 抽卡主界面，包含卡片展示和转盘区域
- **API**: 使用`drawCardsAPI`进行抽卡请求
- **状态管理**: 使用Vue响应式数据管理卡片状态、翻转状态等

### 转盘子组件 (turntable.vue)
- **功能**: 幸运转盘抽奖
- **API**: 使用`getTurntablePrizesAPI`和`spinTurntableAPI`
- **通信**: 通过props接收初始数据，通过emit发送抽奖结果

### API服务 (api/drawCard.ts)
- **功能**: 封装所有抽卡相关的API请求
- **方法**: `drawCardsAPI`, `runTestAPI`
- **类型**: 完整的TypeScript类型定义

## 使用方法

### 1. 访问页面
```
http://localhost:9000/#/pages/drawCard/index?id=用户ID
```

### 2. 功能说明
- **抽取25张卡**: 点击按钮进行抽卡
- **卡片翻转**: 自动逐个翻转显示卡片内容
- **价值统计**: 实时显示总价值，带数字动画效果
- **转盘抽奖**: 根据抽卡结果显示转盘，可进行额外抽奖

### 3. 响应式设计
- 支持移动端和桌面端
- 自适应布局和字体大小
- 流畅的动画效果

## 技术特点

### 1. Vue 3 Composition API
- 使用`<script setup>`语法
- 响应式数据管理
- 生命周期钩子

### 2. TypeScript支持
- 完整的类型定义
- 接口类型安全
- 编译时错误检查

### 3. unibest框架集成
- 使用unibest的HTTP请求方法
- 遵循unibest的项目结构
- 支持多端编译

### 4. 样式实现
- SCSS模块化样式
- CSS动画和过渡效果
- 响应式媒体查询

## 文件结构

```
src/pages/drawCard/
├── index.vue          # 主组件
├── turntable.vue      # 转盘子组件
├── style.css          # 原始样式文件（已废弃）
├── index.html         # 原始HTML文件（已废弃）
└── README.md          # 说明文档

src/api/
├── drawCard.ts        # 抽卡API
└── turntable.ts       # 转盘API
```

## 开发和测试

### 1. 启动开发服务器
```bash
npm run dev
```

### 2. 访问页面
浏览器打开: http://localhost:9000/#/pages/drawCard/index?id=test

### 3. 测试功能
- 测试抽卡功能
- 测试转盘功能
- 测试响应式布局
- 测试API集成

## 注意事项

1. **API依赖**: 需要后端服务运行在`http://localhost:8888`
2. **用户ID**: 需要通过URL参数传递用户ID
3. **浏览器兼容**: 支持现代浏览器，使用了CSS Grid和Flexbox
4. **性能优化**: 使用了Vue的响应式系统，避免不必要的重渲染
