* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.product-card {
    position: relative;
    height: 200px;
    overflow: visible;
    border-radius: 15px;
    perspective: 1000px;
    background-color: transparent;
}

.product-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    transition: transform 0.8s;
    transform-style: preserve-3d;
    border-width: 5px;
    border-style: solid;
    border-radius: 15px;
}

.product-card.is-flipped .product-card-inner {
    transform: rotateY(180deg);
}

.product-card-front,
.product-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    overflow: hidden;
    border-radius: 10px;
}

.product-card-back {
    transform: rotateY(180deg);
}

.product-card-background {
    position: absolute;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(-45deg, #4a4a4a, #4a4a4a 10px, #3a3a3a 10px, #3a3a3a 20px);
    z-index: 1;
    background-attachment: fixed;
}

.product-card-back-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 10px;
    color: white;
    position: relative;
    z-index: 2;
}

.back-header {
    text-align: center;
}

.back-title-en {
    font-size: 13px;
    color: #a4d8a4;
    font-weight: bold;
}

.back-title-cn {
    font-size: 16px;
    font-weight: bold;
    margin-top: 5px;
}

.back-logo {
    margin: 8px 0;
}

.back-footer {
    font-size: 13px;
    font-weight: bold;
}

.product-card-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    z-index: 2;
    color: white;
    padding: 8px;
}

.product-header {
    margin-bottom: 4px;
}

.product-name-label {
    font-size: 10px;
    opacity: 0.7;
}

.product-name-cn {
    font-size: 13px;
    font-weight: bold;
    margin: 2px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.product-name-en {
    font-size: 4px;
    color: #3fafa9;
    font-weight: bold;
}

.product-image-container {
    position: relative;
    height: 70px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.product-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.product-footer {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-top: auto;
}

.product-avgPrice-container {
    text-align: right;
    order: 2;
}

.product-avgPrice-label {
    font-size: 10px;
    opacity: 0.7;
}

.product-avgPrice {
    font-size: 16px;
    font-weight: bold;
}

.product-weight {
    font-size: 10px;
    margin-top: 2px;
}

.grid-icon {
    width: 16px;
    margin-left: 5px;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 1px;
    order: 1;
}

.grid-cell {
    background-color: rgba(255, 255, 255, 0.2);
    height: 3px;
}

.grid-cell.highlight {
    background-color: white;
}

.brand-logo {
    position: absolute;
    bottom: 5px;
    right: 5px;
    font-size: 4px;
    opacity: 0.7;
}

.theme-colorful {
    background: linear-gradient(45deg, red, orange, yellow, green, blue, indigo, violet);
    padding: 5px;
    border-radius: 15px;
}

.theme-colorful .product-card-inner {
    border: none;
    border-radius: 10px;
}

.theme-colorful .product-image-container {
    background: repeating-linear-gradient(-45deg, #4a4a4a, #4a4a4a 10px, #3a3a3a 10px, #3a3a3a 20px);
    background-attachment: fixed;
}

.theme-purple .product-card-inner {
    border-color: #553486
}

.theme-purple .product-image-container {
    background: repeating-linear-gradient(-45deg, #3a3a6a, #3a3a6a 10px, #2a2a5a 10px, #2a2a5a 20px);
    border-color: #553486;
    background-attachment: fixed;
}

.theme-red .product-card-inner {
    border-color: #c00000;
}

.theme-red .product-image-container {
    background: repeating-linear-gradient(-45deg, #6e2a3a, #6e2a3a 10px, #5a1a2a 10px, #5a1a2a 20px);
    border-color: #a00000;
    background-attachment: fixed;
}

.theme-yellow .product-card-inner {
    border-color: #e8a833;
}

.theme-yellow .product-image-container {
    background: repeating-linear-gradient(-45deg, #d4ac0d, #d4ac0d 10px, #b8860b 10px, #b8860b 20px);
    border-color: #c89023;
    background-attachment: fixed;
}

.theme-blue .product-card-inner {
    border-color: #2c7fb8;
}

.theme-blue .product-image-container {
    background: repeating-linear-gradient(-45deg, #3a6a8a, #3a6a8a 10px, #2a5a7a 10px, #2a5a7a 20px);
    border-color: #24689a;
    background-attachment: fixed;
}

.turntable-section {
    display: none; /* 默认隐藏 */
    margin: 20px auto;
    text-align: center;
}

#spinResult {
    margin-top: 15px;
    font-size: 22px;
    font-weight: bold;
    height: 30px;
    transition: color 0.5s;
}

/* 新增卡片价值动画样式 */
.card-value-popup {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(255, 215, 0, 0.8);
    color: #333;
    padding: 3px 8px;
    border-radius: 15px;
    font-weight: bold;
    opacity: 0;
    transition: all 0.5s;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

.card-value-popup.show {
    opacity: 1;
    top: -40px;
} 