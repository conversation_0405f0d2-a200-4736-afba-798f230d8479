@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    --primary-color: #6d28d9;
    --primary-hover: #5b21b6;
    --secondary-color: #1e293b;
    --accent-color: #60a5fa;
}

body {
    font-family: 'Noto Sans SC', 'Montserrat', sans-serif;
    background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
    background-attachment: fixed;
    color: #e2e8f0;
    position: relative;
}

body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.04) 1px, transparent 1px),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.04) 1px, transparent 1px);
    background-size: 50px 50px;
    pointer-events: none;
    z-index: 0;
}

.custom-message {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(to right, rgba(30, 41, 59, 0.9), rgba(109, 40, 217, 0.8));
    border: 1px solid rgba(96, 165, 250, 0.3);
    color: #e2e8f0;
    padding: 15px 20px;
    border-radius: 8px;
    font-size: 16px;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 20px rgba(0,0,0,0.4), 0 0 30px rgba(96, 165, 250, 0.3);
    transition: opacity 0.4s ease-out;
    opacity: 0;
    white-space: nowrap;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}
.custom-message.is-visible {
    opacity: 1;
}

/* 高级标题样式 */
header {
    background: linear-gradient(to right, rgba(0,0,0,0.9), rgba(0,0,0,0.7));
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 20px rgba(0,0,0,0.5);
    position: relative;
    overflow: hidden;
    z-index: 10;
}

header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
    opacity: 0.8;
    z-index: 5;
}

/* 总值显示改进 */
#totalValue {
    background: rgba(15, 23, 42, 0.9);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    z-index: 50;
}

#totalValueCounter {
    font-family: 'Orbitron', sans-serif;
    font-weight: 700;
    text-shadow: 0 0 10px rgba(239, 68, 68, 0.8);
    letter-spacing: 1px;
    background: linear-gradient(90deg, #ff4d4d, #f87171);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { text-shadow: 0 0 10px rgba(239, 68, 68, 0.5); }
    50% { text-shadow: 0 0 20px rgba(239, 68, 68, 0.8); }
    100% { text-shadow: 0 0 10px rgba(239, 68, 68, 0.5); }
}

/* 按钮样式增强 */
#drawButton {
    background: linear-gradient(135deg, #8b5cf6 0%, #6d28d9 100%);
    border: none;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease, transform 0.2s ease;
    box-shadow: 0 8px 16px rgba(109, 40, 217, 0.3);
}

#drawButton::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.7s ease;
}

#drawButton:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(109, 40, 217, 0.4);
}

#drawButton:hover::before {
    left: 100%;
}

#drawButton:disabled {
    background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
    transform: none;
    box-shadow: 0 4px 8px rgba(107, 114, 128, 0.3);
}

#testButton {
    background: rgba(55, 65, 81, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

#testButton:hover {
    background: rgba(75, 85, 99, 0.9);
    box-shadow: 0 6px 16px rgba(0,0,0,0.2);
}

/* 应用区域优化 */
#app {
    padding: 1.5rem;
    min-height: 200px;
    position: relative;
    z-index: 1;
}

/* 测试结果区域美化 */
#testResultSection {
    background: linear-gradient(to bottom right, rgba(30, 41, 59, 0.9), rgba(15, 23, 42, 0.9));
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    box-shadow: 0 10px 25px rgba(0,0,0,0.3);
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
    overflow: hidden;
}

#testResultOutput {
    background: rgba(17, 24, 39, 0.7);
    border-color: rgba(55, 65, 81, 0.5);
    transition: all 0.3s ease;
    font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
}

/* 转盘区域优化 */
.turntable-section {
    position: relative;
    padding: 2rem 0;
    margin-top: 3rem;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
}

#turntable-iframe {
    transition: all 0.3s ease;
    box-shadow: 0 15px 30px rgba(0,0,0,0.4);
}

#spinResult {
    font-size: 1.125rem;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.5rem;
    text-align: center;
} 