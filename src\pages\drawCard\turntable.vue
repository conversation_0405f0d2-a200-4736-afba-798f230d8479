<script lang="ts" setup>
import type { IPrize, IPrizesResponse, ISpinResponse } from '@/api/turntable'
import { nextTick, onMounted, ref, watch } from 'vue'
import { getTurntablePrizesAPI, spinTurntableAPI } from '@/api/turntable'

// 定义组件props
interface Props {
  userId?: string
  shouldInitialize?: boolean
  addSpinsCount?: number
}

const props = withDefaults(defineProps<Props>(), {
  userId: '',
  shouldInitialize: false,
  addSpinsCount: 0,
})

// 定义组件emits
const emit = defineEmits<{
  turntableReady: []
  spinResult: [prize: any]
  initialSpinsValue: [value: number]
}>()

// 响应式数据
const boxRef = ref<HTMLElement>()
const btnRef = ref<HTMLElement>()
const clickLock = ref(false)
const spinCount = ref(0)
const wheelPrizes = ref<IPrize[]>([])
const isInitialized = ref(false)
const spinHistory = ref<any[]>([])
const showHistory = ref(false)

// 按钮状态
const buttonText = ref('抽奖')
const buttonCursor = ref('pointer')
const buttonDisabled = ref(false)

// 转盘旋转状态
const boxRotation = ref(0)
const btnRotation = ref(0)

// 奖品颜色配置
const prizeColors = ['#ff7875', '#ff9c6e', '#ffc069', '#d3f261', '#95de64', '#5cdbd3']

// 使用useRequest管理获取奖品信息的请求状态
const {
  loading: prizesLoading,
  error: prizesError,
  data: prizesData,
  run: fetchPrizes,
} = useRequest<IPrizesResponse>(() => getTurntablePrizesAPI(props.userId), {
  immediate: false,
})

// 使用useRequest管理抽奖请求状态
const {
  loading: spinLoading,
  error: spinError,
  data: spinData,
  run: executeSpin,
} = useRequest<ISpinResponse>(() => spinTurntableAPI(props.userId), {
  immediate: false,
})

// 初始化转盘
async function initializeTurntable() {
  if (isInitialized.value)
    return
  isInitialized.value = true

  try {
    // 如果没有userId，使用模拟数据
    if (!props.userId) {
      // 模拟奖品数据
      wheelPrizes.value = [
        { name: '现金红包', value: 10 },
        { name: '优惠券', value: 5 },
        { name: '积分', value: 100 },
        { name: '谢谢参与', value: 0 },
        { name: '小礼品', value: 20 },
        { name: '再来一次', value: 0 },
      ]
      spinCount.value = 3
      emit('turntableReady')
      updateButtonText()
      return
    }

    // 使用useRequest获取奖品数据
    const data = await fetchPrizes()
    console.log(data)

    if (data) {
      wheelPrizes.value = data.prizes
      spinCount.value = data.remainingSpins

      if (data.spinResults && data.spinResults.length > 0) {
        spinHistory.value = []
        let totalPrizeValueFromHistory = 0

        data.spinResults.forEach((result) => {
          totalPrizeValueFromHistory += result.value || 0
          const prizeTime = new Date(result.timestamp).toLocaleString()
          spinHistory.value.push({
            name: result.name,
            time: prizeTime,
          })
        })

        if (totalPrizeValueFromHistory > 0) {
          emit('initialSpinsValue', totalPrizeValueFromHistory)
        }
        showHistory.value = true
      }

      emit('turntableReady')
      updateButtonText()
    }
  }
  catch (error) {
    console.error('Failed to initialize turntable prizes:', error)
    // 如果API调用失败，使用模拟数据
    wheelPrizes.value = [
      { name: '现金红包', value: 10 },
      { name: '优惠券', value: 5 },
      { name: '积分', value: 100 },
      { name: '谢谢参与', value: 0 },
      { name: '小礼品', value: 20 },
      { name: '再来一次', value: 0 },
    ]
    spinCount.value = 3
    buttonText.value = '初始化失败'
    emit('turntableReady')
    updateButtonText()
  }
}

// 更新按钮文本
function updateButtonText() {
  if (spinCount.value > 0) {
    buttonText.value = `抽奖 (${spinCount.value})`
    buttonCursor.value = 'pointer'
    buttonDisabled.value = false
  }
  else {
    buttonText.value = '无抽奖次数'
    buttonCursor.value = 'not-allowed'
    buttonDisabled.value = true
  }
}

// 抽奖点击处理
async function handleSpin() {
  if (clickLock.value || spinCount.value <= 0 || buttonDisabled.value) {
    return
  }
  clickLock.value = true
  buttonDisabled.value = true

  try {
    let prize: IPrize
    let prizeIndex: number

    // 如果没有userId，使用模拟逻辑
    if (!props.userId) {
      // 模拟抽奖逻辑
      prizeIndex = Math.floor(Math.random() * wheelPrizes.value.length)
      prize = wheelPrizes.value[prizeIndex]
      spinCount.value = Math.max(0, spinCount.value - 1)
    }
    else {
      // 使用useRequest执行抽奖
      const data = await executeSpin()
      if (data) {
        prize = data.prize
        prizeIndex = data.prizeIndex
        spinCount.value = data.remainingSpins
      }
      else {
        throw new Error('抽奖失败')
      }
    }

    updateButtonText()

    // Map the prize index from the logical array to the visual one
    const visualIndexMap: Record<number, number> = { 0: 0, 1: 1, 2: 2, 3: 5, 4: 4, 5: 3 }
    const visualPrizeIndex = visualIndexMap[prizeIndex] || prizeIndex

    run(visualPrizeIndex * 60 + 30, prize)

    // 添加到历史记录
    const prizeTime = new Date().toLocaleString()
    spinHistory.value.unshift({
      name: prize.name,
      time: prizeTime,
    })
    showHistory.value = true
  }
  catch (error) {
    console.error('Spin failed:', error)
    // 如果API调用失败，使用模拟逻辑
    const prizeIndex = Math.floor(Math.random() * wheelPrizes.value.length)
    const prize = wheelPrizes.value[prizeIndex]
    spinCount.value = Math.max(0, spinCount.value - 1)
    updateButtonText()

    const visualIndexMap: Record<number, number> = { 0: 0, 1: 1, 2: 2, 3: 5, 4: 4, 5: 3 }
    const visualPrizeIndex = visualIndexMap[prizeIndex] || prizeIndex

    run(visualPrizeIndex * 60 + 30, prize)

    const prizeTime = new Date().toLocaleString()
    spinHistory.value.unshift({
      name: prize.name,
      time: prizeTime,
    })
    showHistory.value = true
  }
}

// 转盘旋转动画
let timer: NodeJS.Timeout | null = null
function run(angle: number, prize: any) {
  let current = 0
  const base = 1800
  const finalAngle = base + angle
  const ratio = 0.1

  timer = setInterval(() => {
    if (current >= finalAngle) {
      clickLock.value = false
      if (timer)
        clearInterval(timer)
      updateButtonText() // 重新更新按钮状态
      emit('spinResult', prize)
    }
    const step = (finalAngle - current) * ratio
    current += Math.ceil(step)

    // 使用响应式数据更新旋转角度
    boxRotation.value = current
    btnRotation.value = -current
  }, 16)
}

// 监听props变化
watch(() => props.shouldInitialize, (newVal) => {
  if (newVal) {
    initializeTurntable()
  }
})

watch(() => props.addSpinsCount, (newVal) => {
  if (newVal > 0) {
    spinCount.value += newVal
    updateButtonText()
  }
})

onMounted(() => {
  nextTick(() => {
    if (props.shouldInitialize) {
      initializeTurntable()
    }
  })
})
</script>

<template>
  <view class="turntable-container">
    <text class="turntable-title">
      幸运大转盘
    </text>

    <view
      id="box"
      ref="boxRef"
      class="turntable-box"
      :style="{ transform: `rotate(${boxRotation}deg)` }"
    >
      <!-- 左侧奖品区域 -->
      <view class="left">
        <view
          v-for="(prize, index) in wheelPrizes.slice(0, 3)"
          :key="`left-${index}`"
          class="item left-item"
          :style="{
            '--i': index === 0 ? 0 : index === 1 ? -1 : -2,
            '--clr': prizeColors[index] || '#ff7875',
          }"
        >
          <text class="prize-text">
            {{ prize?.name || '' }}
          </text>
        </view>
      </view>

      <!-- 右侧奖品区域 -->
      <view class="right">
        <view
          v-for="(prize, index) in wheelPrizes.slice(3, 6).reverse()"
          :key="`right-${index}`"
          class="item right-item"
          :style="{
            '--i': index,
            '--clr': prizeColors[index + 3] || '#d3f261',
          }"
        >
          <text class="prize-text">
            {{ prize?.name || '' }}
          </text>
        </view>
      </view>

      <!-- 抽奖按钮 -->
      <view
        id="btn"
        ref="btnRef"
        class="spin-btn"
        :class="{ disabled: buttonDisabled }"
        :style="{
          transform: `translate(-50%, -50%) rotate(${btnRotation}deg)`,
          cursor: buttonCursor,
        }"
        @click="handleSpin"
      >
        {{ buttonText }}
      </view>
    </view>

    <!-- 抽奖历史记录 -->
    <view v-if="showHistory" class="spin-history">
      <text class="history-title">
        抽奖记录
      </text>
      <scroll-view class="history-list" scroll-y>
        <view
          v-for="(record, index) in spinHistory"
          :key="index"
          class="history-item"
        >
          抽中: {{ record.name }} - {{ record.time }}
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.turntable-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  background-color: #0f172a;
  padding: 20px;
}

.turntable-title {
  font-size: 1.875rem; /* 30px */
  line-height: 2.25rem; /* 36px */
  font-weight: 700;
  background: linear-gradient(90deg, #60a5fa, #8b5cf6);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-align: center;
  margin-bottom: 1.5rem; /* 24px */
  text-shadow: 0 2px 10px rgba(139, 92, 246, 0.3);
  font-family: 'Noto Sans SC', 'Montserrat', sans-serif;
}

.turntable-box {
  position: relative;
  width: 300px;
  height: 300px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.left {
  width: 150px;
  height: 300px;
  overflow: hidden;
  position: absolute;
  left: 0;
}

.left-item {
  position: absolute;
  width: 150px;
  height: 150px;
  background-color: var(--clr);
  transform-origin: right bottom;
  transform: rotate(calc(60deg * var(--i)));
}

.left .prize-text {
  position: absolute;
  left: 70px;
  top: 30px;
  transform: rotate(-30deg);
  font-size: 12px;
  font-weight: bold;
  color: #333;
  text-align: center;
  width: 60px;
}

.right {
  width: 150px;
  height: 300px;
  overflow: hidden;
  position: absolute;
  right: 0;
}

.right-item {
  position: absolute;
  width: 150px;
  height: 150px;
  background-color: var(--clr);
  transform-origin: left bottom;
  transform: rotate(calc(60deg * var(--i)));
}

.right .prize-text {
  position: absolute;
  right: 70px;
  top: 30px;
  transform: rotate(30deg);
  font-size: 12px;
  font-weight: bold;
  color: #333;
  text-align: center;
  width: 60px;
}

.spin-btn {
  width: 80px;
  height: 80px;
  background-color: #fff;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  user-select: none;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  color: #333;
  font-size: 14px;
}

.spin-btn::after {
  position: absolute;
  top: -30px;
  content: '';
  width: 40px;
  height: 40px;
  background-color: #fff;
  -webkit-clip-path: polygon(50% 0%, 25% 100%, 75% 100%);
  clip-path: polygon(50% 0%, 25% 100%, 75% 100%);
}

.spin-btn.disabled {
  opacity: 0.6;
  cursor: not-allowed !important;
}

.spin-btn.disabled::after {
  opacity: 0.6;
}

.spin-history {
  margin-top: 20px;
  width: 300px;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.history-title {
  margin-bottom: 10px;
  color: #333;
  font-weight: bold;
  font-size: 16px;
}

.history-list {
  max-height: 200px;
}

.history-item {
  padding: 8px 5px;
  border-bottom: 1px solid #eee;
  color: #666;
  font-size: 14px;
}

.history-item:last-child {
  border-bottom: none;
}
</style>
