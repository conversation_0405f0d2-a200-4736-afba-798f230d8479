<h2>幸运大转盘</h2>
<div id="box">
    <div class="left">
        <div class="item" style="--i:0;--clr:#ff7875;"></div>
        <div class="item" style="--i:-1;--clr:#ff9c6e;"></div>
        <div class="item" style="--i:-2;--clr:#ffc069;"></div>
    </div>
    <div class="right">
        <div class="item" style="--i:0;--clr:#d3f261;"></div>
        <div class="item" style="--i:1;--clr:#95de64;"></div>
        <div class="item" style="--i:2;--clr:#5cdbd3;"></div>
    </div>
    <div id="btn">抽奖</div>
</div>
<div id="spin-history" style="display: none;">
    <h3>抽奖记录</h3>
    <ul></ul>
</div>
<style>
    h2 {
        font-size: 1.875rem; /* 30px */
        line-height: 2.25rem; /* 36px */
        font-weight: 700;
        background: linear-gradient(90deg, #60a5fa, #8b5cf6);
        background-clip: text;
        -webkit-background-clip: text;
        color: transparent;
        text-align: center;
        margin-bottom: 1.5rem; /* 24px */
        text-shadow: 0 2px 10px rgba(139, 92, 246, 0.3);
        font-family: 'Noto Sans SC', 'Montserrat', sans-serif;
    }
    * {
        padding: 0;
        margin: 0;
    }

    body {
        width: 100vw;
        height: 100vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background-color: #0f172a;
    }

    #box {
        position: relative;
        width: 300px;
        height: 300px;
        border-radius: 50%;
        overflow: hidden;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .left {
        width: 150px;
        height: 300px;
        overflow: hidden;
        position: absolute;
        left: 0;
    }

    .left .item {
        position: absolute;
        width: 150px;
        height: 150px;
        background-color: var(--clr);
        transform-origin: right bottom;
        transform: rotate(calc(60deg * var(--i)));
    }

    .left .item span {
        position: absolute;
        left: 70px;
        top: 30px;
        transform: rotate(-30deg);
    }

    .right {
        width: 150px;
        height: 300px;
        overflow: hidden;
        position: absolute;
        right: 0;
    }

    .right .item {
        position: absolute;
        width: 150px;
        height: 150px;
        background-color: var(--clr);
        transform-origin: left bottom;
        transform: rotate(calc(60deg * var(--i)));
    }

    .right .item span {
        position: absolute;
        right: 70px;
        top: 30px;
        transform: rotate(30deg);
    }

    #btn {
        width: 80px;
        height: 80px;
        background-color: #fff;
        border-radius: 50%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        cursor: pointer;
        user-select: none;

        display: flex;
        justify-content: center;
        align-items: center;
    }

    #btn::after {
        position: absolute;
        top: -30px;
        content: "";
        width: 40px;
        height: 40px;
        background-color: #fff;
        -webkit-clip-path: polygon(50% 0%, 25% 100%, 75% 100%);
        clip-path: polygon(50% 0%, 25% 100%, 75% 100%);
    }

    #spin-history {
        margin-top: 20px;
        width: 300px;
        text-align: center;
        background-color: #f9f9f9;
        border-radius: 8px;
        padding: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    #spin-history h3 {
        margin-bottom: 10px;
        color: #333;
    }

    #spin-history ul {
        list-style: none;
        padding: 0;
        margin: 0;
        max-height: 200px;
        overflow-y: auto;
    }

    #spin-history li {
        padding: 8px 5px;
        border-bottom: 1px solid #eee;
        color: #666;
        font-size: 14px;
    }

    #spin-history li:last-child {
        border-bottom: none;
    }
</style>
<script>
    const box = document.getElementById("box"),
        itemEls = document.querySelectorAll(".item"),
        btn = document.getElementById("btn");
    let clickLock = false;
    let spinCount = 0;
    let wheelPrizes = [];
    let userId = null;
    let isInitialized = false;

    const apiBaseUrl = '/api';

    async function initializeTurntable() {
        if (isInitialized) return;
        isInitialized = true;
        try {
            const urlParams = new URLSearchParams(window.location.search);
            userId = urlParams.get('id');
            // const response = await fetch(`http://localhost:3000/api/prizes?id=${userId}`); // 改为获取奖品信息的专用接口
            const response = await fetch(`${apiBaseUrl}/Mon/prizes?id=${userId}`); // 改为获取奖品信息的专用接口
            const data = await response.json();
            wheelPrizes = data.prizes;
            spinCount = data.remainingSpins;
            
            if (data.spinResults && data.spinResults.length > 0) {
                const historyContainer = document.getElementById("spin-history");
                const historyList = historyContainer.querySelector("ul");
                historyList.innerHTML = '';
                let totalPrizeValueFromHistory = 0;
                data.spinResults.forEach(result => {
                    totalPrizeValueFromHistory += result.value || 0;
                    const listItem = document.createElement('li');
                    const prizeTime = new Date(result.timestamp).toLocaleString();
                    listItem.textContent = `抽中: ${result.name} - ${prizeTime}`;
                    historyList.appendChild(listItem);
                });
                if (totalPrizeValueFromHistory > 0) {
                    window.parent.postMessage({ type: 'initialSpinsValue', value: totalPrizeValueFromHistory }, '*');
                }
                historyContainer.style.display = 'block';
            }

            // Re-order for display to match original visual layout
            const displayPrizes = [
                wheelPrizes[0], wheelPrizes[1], wheelPrizes[2], 
                wheelPrizes[5], wheelPrizes[4], wheelPrizes[3]
            ];

            const leftPrizesData = displayPrizes.slice(0, 3);
            const rightPrizesData = displayPrizes.slice(3, 6).reverse(); // .reverse() to match original item order

            for (let i = 0; i < 3; i++) {
                itemEls[i].innerHTML = `<span>${leftPrizesData[i].name}</span>`;
                itemEls[i + 3].innerHTML = `<span>${rightPrizesData[i].name}</span>`;
            }

            window.parent.postMessage({ type: 'turntableReady' }, '*');
            updateButtonText();

        } catch (error) {
            console.error("Failed to initialize turntable prizes:", error);
            btn.textContent = "初始化失败";
        }
    }

    function updateButtonText() {
        if (spinCount > 0) {
            btn.textContent = `抽奖 (${spinCount})`;
            btn.style.cursor = 'pointer';
        } else {
            btn.textContent = '无抽奖次数';
            btn.style.cursor = 'not-allowed';
        }
    }

    btn.addEventListener("click", async () => {
        if (clickLock || spinCount <= 0) {
            return;
        }
        clickLock = true;
        
        try {
            // const response = await fetch(`http://localhost:3000/api/spin?id=${userId}`);
            const response = await fetch(`${apiBaseUrl}/Mon/spin?id=${userId}`);
            if (!response.ok) {
                throw new Error(`Server error: ${response.status}`);
            }
            const data = await response.json();
            const { prize, prizeIndex, remainingSpins } = data;

            spinCount = remainingSpins;
            updateButtonText();
            
            // Map the prize index from the logical array to the visual one
            const visualIndexMap = { 0:0, 1:1, 2:2, 3:5, 4:4, 5:3 };
            const visualPrizeIndex = visualIndexMap[prizeIndex];

            run(visualPrizeIndex * 60 + 30, prize);
            
            const historyContainer = document.getElementById("spin-history");
            const historyList = historyContainer.querySelector("ul");
            const newResultItem = document.createElement('li');
            const prizeTime = new Date().toLocaleString();
            newResultItem.textContent = `抽中: ${prize.name} - ${prizeTime}`;
            historyList.prepend(newResultItem);
            historyContainer.style.display = 'block';

        } catch (error) {
            console.error("Spin failed:", error);
            alert(`抽奖失败: ${error.message}`);
            clickLock = false;
        }
    });

    let timer = null;
    function run(angle, prize) {
        let current = 0;
        let base = 1800;
        const finalAngle = base + angle;
        const ratio = 0.1;
        timer = setInterval(function () {
            if (current >= finalAngle) {
                clickLock = false;
                clearInterval(timer);
                window.parent.postMessage({ type: 'spinResult', prize: prize }, '*');
            }
            const step = (finalAngle - current) * ratio;
            current += Math.ceil(step);
            box.style.transform = `rotate(${current}deg)`;
            btn.style.transform = `translate(-50%, -50%) rotate(${-current}deg)`;
        }, 16);
    }

    window.addEventListener('message', (event) => {
        if (event.data.type === 'initialize') {
            initializeTurntable();
        } else if (event.data.type === 'addSpins') {
            spinCount += event.data.count;
            updateButtonText();
        }
    });

</script>