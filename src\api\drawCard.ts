import { http } from '@/http/http'

// 产品卡片接口类型定义
export interface IProduct {
  objectName: string
  prePic: string
  avgPrice: number
  weight: number
  width: number
  length: number
  theme: string
}

// 抽卡响应类型
export interface IDrawResponse {
  cards: IProduct[]
  alreadyDrawn: boolean
  turntableSpins: number
}

// 测试响应类型
export interface ITestResponse {
  numTests: number
  maxValue: number
  minValue: number
  averageValue: number
  drawValues: number[]
}

/**
 * 抽取卡片
 * @param userId 用户ID
 * @returns 抽卡结果
 */
export function drawCardsAPI(userId: string) {
  return http.get<IDrawResponse>('/Mon/draw', { id: userId })
}

/**
 * 运行测试
 * @param userId 用户ID
 * @returns 测试结果
 */
export function runTestAPI(userId: string) {
  return http.get<ITestResponse>('/Mon/test', { id: userId })
}
